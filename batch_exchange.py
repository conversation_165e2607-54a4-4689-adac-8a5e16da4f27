#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量兑换码自动化脚本
支持多个玩家和多个兑换码的批量处理
"""

import json
import time
import sys
import os
from browser_automation import WJDRAutoExchange

class BatchExchange:
    def __init__(self, config_file="config.json"):
        """
        初始化批量兑换

        Args:
            config_file (str): 配置文件路径
        """
        self.config_file = config_file
        self.config = self.load_config()
        self.automation = None

    def load_config(self):
        """加载配置文件"""
        try:
            # 尝试使用utf-8-sig编码处理BOM问题
            try:
                with open(self.config_file, 'r', encoding='utf-8-sig') as f:
                    config = json.load(f)
            except UnicodeDecodeError:
                # 如果utf-8-sig失败，尝试普通utf-8
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

            print(f"✅ 配置文件加载成功: {self.config_file}")
            return config
        except FileNotFoundError:
            print(f"❌ 配置文件不存在: {self.config_file}")
            print("请确保 config.json 文件存在于当前目录")
            sys.exit(1)
        except json.JSONDecodeError as e:
            print(f"❌ 配置文件格式错误: {e}")
            print("请检查 config.json 文件的JSON格式是否正确")
            sys.exit(1)
        except Exception as e:
            print(f"❌ 加载配置文件失败: {e}")
            sys.exit(1)

    def validate_config(self):
        """验证配置文件"""
        required_keys = ['players', 'gift_codes']
        for key in required_keys:
            if key not in self.config:
                print(f"❌ 配置文件缺少必要字段: {key}")
                return False

        # 支持新的对象格式和旧的数组格式
        players = self.config['players']
        if isinstance(players, dict):
            if not players:
                print("❌ 玩家字典为空")
                return False
        elif isinstance(players, list):
            if not players:
                print("❌ 玩家列表为空")
                return False
        else:
            print("❌ 玩家配置格式错误，应为对象或数组")
            return False

        if not self.config['gift_codes']:
            print("❌ 兑换码列表为空")
            return False

        print("✅ 配置文件验证通过")
        return True

    def setup_automation(self):
        """设置自动化实例"""
        try:
            headless = self.config.get('settings', {}).get('headless', False)
            self.automation = WJDRAutoExchange(headless=headless)
            print("✅ 浏览器自动化设置完成")
        except Exception as e:
            print(f"❌ 浏览器自动化设置失败: {e}")
            raise

    def exchange_for_player(self, player_id, gift_codes, player_name=None):
        """
        为单个玩家兑换所有礼品码

        Args:
            player_id (str): 玩家ID
            gift_codes (list): 兑换码列表
            player_name (str): 玩家名称（可选）

        Returns:
            dict: 兑换结果统计
        """
        display_name = f"{player_name}({player_id})" if player_name else player_id
        print(f"\n👤 开始为玩家 {display_name} 兑换礼品码...")

        results = {
            'player_id': player_id,
            'player_name': player_name,
            'total_codes': len(gift_codes),
            'new_success_count': 0,  # 新兑换成功
            'already_claimed_count': 0,  # 已经领取过
            'failed_count': 0,  # 真正失败
            'total_success_count': 0,  # 总成功数（新成功 + 已领取）
            'details': []
        }

        max_retries = self.config.get('settings', {}).get('max_retries', 3)
        delay_between_codes = self.config.get('delay', 5)

        for i, gift_code in enumerate(gift_codes):
            print(f"\n🎁 [{i+1}/{len(gift_codes)}] 正在兑换: {gift_code}")

            try:
                # 使用新的详细兑换方法
                success, result_info = self.automation.exchange_gift_code_with_details(player_id, gift_code, max_retries)

                if success:
                    # 成功分为两种：新成功和已领取
                    if result_info.get('status') == 'new_success':
                        results['new_success_count'] += 1
                        results['details'].append({
                            'code': gift_code,
                            'status': 'new_success',
                            'message': result_info.get('message', '兑换成功')
                        })
                        print(f"🎉 {gift_code} 兑换成功")
                    else:  # already_claimed
                        results['already_claimed_count'] += 1
                        results['details'].append({
                            'code': gift_code,
                            'status': 'already_claimed',
                            'message': result_info.get('message', '已经领取过该礼物')
                        })
                        print(f"✅ {gift_code} 已经领取过（算作成功）")
                else:
                    results['failed_count'] += 1
                    results['details'].append({
                        'code': gift_code,
                        'status': 'failed',
                        'message': result_info.get('message', '兑换失败')
                    })
                    print(f"❌ {gift_code} 兑换失败")

                # 如果不是最后一个兑换码，等待一段时间
                if i < len(gift_codes) - 1:
                    print(f"⏳ 等待 {delay_between_codes} 秒后继续...")
                    time.sleep(delay_between_codes)

            except KeyboardInterrupt:
                print("\n⚠️ 用户中断操作")
                break
            except Exception as e:
                print(f"❌ 兑换 {gift_code} 时出错: {e}")
                results['failed_count'] += 1
                results['details'].append({
                    'code': gift_code,
                    'status': 'error',
                    'message': str(e)
                })

        # 计算总成功数
        results['total_success_count'] = results['new_success_count'] + results['already_claimed_count']
        # 向后兼容：添加旧字段名
        results['success_count'] = results['total_success_count']

        return results

    def run_batch_exchange(self):
        """执行批量兑换"""
        print("🎮 WJDR 批量兑换码自动化工具")
        print("=" * 50)

        # 验证配置
        if not self.validate_config():
            return

        # 处理配置信息，支持新旧格式
        players_config = self.config['players']
        gift_codes = self.config['gift_codes']

        # 转换为统一格式：[(player_name, player_id), ...]
        if isinstance(players_config, dict):
            # 新格式：对象
            players_list = [(name, player_id) for name, player_id in players_config.items()]
        else:
            # 旧格式：数组
            players_list = [(None, player_id) for player_id in players_config]

        print(f"📋 配置信息:")
        print(f"   玩家数量: {len(players_list)}")
        print(f"   兑换码数量: {len(gift_codes)}")
        print(f"   总兑换次数: {len(players_list) * len(gift_codes)}")

        # 显示玩家列表
        if isinstance(players_config, dict):
            print(f"📝 玩家列表:")
            for name, player_id in players_list:
                print(f"   {name}: {player_id}")

        # 设置自动化
        try:
            self.setup_automation()
        except Exception:
            return

        # 执行批量兑换
        all_results = []

        try:
            for i, (player_name, player_id) in enumerate(players_list):
                display_name = f"{player_name}({player_id})" if player_name else player_id
                print(f"\n🎯 [{i+1}/{len(players_list)}] 处理玩家: {display_name}")

                result = self.exchange_for_player(player_id, gift_codes, player_name)
                all_results.append(result)

                # 显示当前玩家的结果
                print(f"\n📊 玩家 {display_name} 兑换结果:")
                print(f"   🎉 新成功: {result['new_success_count']}/{result['total_codes']}")
                print(f"   ✅ 已领取: {result['already_claimed_count']}/{result['total_codes']}")
                print(f"   📈 总成功: {result['total_success_count']}/{result['total_codes']}")
                print(f"   ❌ 失败: {result['failed_count']}/{result['total_codes']}")

                # 如果不是最后一个玩家，等待一段时间
                if i < len(players_list) - 1:
                    delay = self.config.get('delay', 5)
                    print(f"⏳ 等待 {delay} 秒后处理下一个玩家...")
                    time.sleep(delay)

        except KeyboardInterrupt:
            print("\n⚠️ 批量兑换被用户中断")
        except Exception as e:
            print(f"\n❌ 批量兑换过程中出错: {e}")
        finally:
            if self.automation:
                self.automation.close()

        # 显示总结果
        self.show_summary(all_results)

        # 保存结果
        self.save_results(all_results)

    def show_summary(self, all_results):
        """显示总结果"""
        print("\n" + "=" * 50)
        print("📊 批量兑换总结")
        print("=" * 50)

        total_new_success = sum(r['new_success_count'] for r in all_results)
        total_already_claimed = sum(r['already_claimed_count'] for r in all_results)
        total_failed = sum(r['failed_count'] for r in all_results)
        total_success = total_new_success + total_already_claimed
        total_attempts = total_success + total_failed

        print(f"总兑换次数: {total_attempts}")
        print(f"🎉 新成功次数: {total_new_success}")
        print(f"✅ 已领取次数: {total_already_claimed}")
        print(f"📈 总成功次数: {total_success}")
        print(f"❌ 失败次数: {total_failed}")
        print(f"成功率: {(total_success/total_attempts*100):.1f}%" if total_attempts > 0 else "成功率: 0%")

        print("\n📋 详细结果:")
        for result in all_results:
            player_id = result['player_id']
            player_name = result.get('player_name')
            display_name = f"{player_name}({player_id})" if player_name else player_id

            # 计算各种成功率
            new_success_rate = (result['new_success_count']/result['total_codes']*100) if result['total_codes'] > 0 else 0
            already_claimed_rate = (result['already_claimed_count']/result['total_codes']*100) if result['total_codes'] > 0 else 0
            total_success_rate = (result['total_success_count']/result['total_codes']*100) if result['total_codes'] > 0 else 0
            failed_rate = (result['failed_count']/result['total_codes']*100) if result['total_codes'] > 0 else 0

            print(f"  玩家 {display_name}:")
            print(f"    🎉 新成功: {result['new_success_count']}/{result['total_codes']} ({new_success_rate:.1f}%)")
            print(f"    ✅ 已领取: {result['already_claimed_count']}/{result['total_codes']} ({already_claimed_rate:.1f}%)")
            print(f"    📈 总成功: {result['total_success_count']}/{result['total_codes']} ({total_success_rate:.1f}%)")
            print(f"    ❌ 失败: {result['failed_count']}/{result['total_codes']} ({failed_rate:.1f}%)")

    def save_results(self, all_results):
        """保存结果到文件"""
        try:
            # 获取当前时间
            now = time.localtime()
            timestamp = time.strftime("%Y%m%d_%H%M%S", now)

            # 创建按日期分层的文件夹结构: results/YYYY/MM/DD/
            year = time.strftime("%Y", now)
            month = time.strftime("%m", now)
            day = time.strftime("%d", now)

            # 构建文件夹路径
            results_dir = os.path.join("results", year, month, day)

            # 创建文件夹（如果不存在）
            os.makedirs(results_dir, exist_ok=True)

            # 构建完整的文件路径
            filename = f"exchange_results_{timestamp}.json"
            filepath = os.path.join(results_dir, filename)

            # 保存文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(all_results, f, ensure_ascii=False, indent=2)

            print(f"\n💾 结果已保存到: {filepath}")
            print(f"📁 文件夹结构: results/{year}/{month}/{day}/")
        except Exception as e:
            print(f"\n❌ 保存结果失败: {e}")

def main():
    """主函数"""
    try:
        batch_exchange = BatchExchange()
        batch_exchange.run_batch_exchange()
    except KeyboardInterrupt:
        print("\n⚠️ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")

if __name__ == "__main__":
    main()
