#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器自动化兑换码脚本
使用Selenium模拟真实浏览器操作，绕过签名验证
"""

import time
import json
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import ddddocr

class WJDRAutoExchange:
    def __init__(self, headless=False):
        """
        初始化浏览器自动化

        Args:
            headless (bool): 是否使用无头模式（不显示浏览器窗口）
        """
        self.headless = headless
        self.driver = None
        self.wait = None
        self.ocr = ddddocr.DdddOcr()
        self.setup_driver()

    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            print("🚀 正在启动浏览器...")

            # Chrome选项
            chrome_options = Options()
            if self.headless:
                chrome_options.add_argument('--headless')

            # 添加一些常用选项
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36')

            # 自动下载并设置ChromeDriver
            service = Service(ChromeDriverManager().install())

            # 创建浏览器实例
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)

            print("✅ 浏览器启动成功！")

        except Exception as e:
            print(f"❌ 浏览器启动失败: {e}")
            raise

    def open_website(self):
        """打开兑换码网站"""
        try:
            url = "https://wjdr-giftcode.centurygames.cn/"
            print(f"🌐 正在打开网站: {url}")

            self.driver.get(url)

            # 等待页面加载
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))

            print("✅ 网站加载成功！")
            time.sleep(2)  # 等待页面完全加载

        except TimeoutException:
            print("❌ 网站加载超时")
            raise
        except Exception as e:
            print(f"❌ 打开网站失败: {e}")
            raise

    def input_player_id(self, player_id):
        """输入玩家ID"""
        try:
            print(f"👤 正在输入玩家ID: {player_id}")

            # 查找玩家ID输入框 - 基于实际HTML结构
            selectors = [
                "input[placeholder='角色ID']",
                "input[placeholder*='角色ID']",
                "input[placeholder*='Player ID']",
                "input[placeholder*='玩家ID']",
                "input[name='fid']",
                "input[id*='fid']",
                ".fid input",
                "#fid"
            ]

            player_input = None
            for selector in selectors:
                try:
                    player_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue

            if not player_input:
                # 如果找不到，尝试通过文本查找
                inputs = self.driver.find_elements(By.TAG_NAME, "input")
                for inp in inputs:
                    placeholder = inp.get_attribute("placeholder") or ""
                    if any(keyword in placeholder.lower() for keyword in ["id", "角色", "player", "玩家"]):
                        player_input = inp
                        break

            if player_input:
                player_input.clear()
                player_input.send_keys(player_id)
                print("✅ 玩家ID输入成功！")
                return True
            else:
                print("❌ 找不到玩家ID输入框")
                return False

        except Exception as e:
            print(f"❌ 输入玩家ID失败: {e}")
            return False

    def click_login_button(self):
        """点击登录按钮"""
        try:
            print("🔑 正在点击登录按钮...")

            # 查找登录按钮 - 基于实际HTML结构
            login_selectors = [
                ".btn.login_btn",  # 实际的登录按钮class
                "div.login_btn",
                ".login_btn",
                "button:contains('登录')",
                "div:contains('登录')",
                "button:contains('Login')",
                "button:contains('确认')",
                "button:contains('查询')",
                "button:contains('获取')",
                "button:contains('验证')",
                ".confirm-btn",
                ".query-btn",
                ".verify-btn",
                "input[type='button'][value*='登录']",
                "input[type='submit'][value*='登录']",
                "input[type='button'][value*='查询']",
                "input[type='submit'][value*='查询']",
                "input[type='button'][value*='确认']",
                "input[type='submit'][value*='确认']"
            ]

            login_button = None
            for selector in login_selectors:
                try:
                    if ":contains(" in selector:
                        # 使用XPath查找包含文本的按钮
                        text = selector.split(":contains('")[1].split("')")[0]
                        login_button = self.driver.find_element(By.XPATH, f"//button[contains(text(), '{text}')]")
                    else:
                        login_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue

            if not login_button:
                # 尝试查找所有按钮，根据文本内容判断
                buttons = self.driver.find_elements(By.TAG_NAME, "button")
                for btn in buttons:
                    btn_text = btn.text.lower()
                    if any(keyword in btn_text for keyword in ["登录", "login", "确认", "查询"]):
                        login_button = btn
                        break

            if login_button:
                # 滚动到按钮位置
                self.driver.execute_script("arguments[0].scrollIntoView();", login_button)
                time.sleep(1)

                # 点击按钮
                login_button.click()
                print("✅ 登录按钮点击成功！")

                # 等待登录结果
                time.sleep(3)
                return True
            else:
                print("❌ 找不到登录按钮")
                return False

        except Exception as e:
            print(f"❌ 点击登录按钮失败: {e}")
            return False

    def get_captcha_and_solve(self):
        """获取验证码并识别"""
        try:
            print("🔍 正在获取验证码...")

            # 查找验证码图片 - 基于实际HTML结构
            captcha_selectors = [
                "img.verify_pic",  # 实际的验证码图片class
                ".verify_pic",
                "img[src*='captcha']",
                ".captcha img",
                ".verify img",
                "img[alt*='验证码']",
                "img[alt*='captcha']"
            ]

            captcha_img = None
            for selector in captcha_selectors:
                try:
                    captcha_img = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue

            if not captcha_img:
                print("ℹ️ 未发现验证码图片")
                return None

            # 获取验证码图片
            captcha_screenshot = captcha_img.screenshot_as_png

            # 使用OCR识别验证码
            captcha_text = self.ocr.classification(captcha_screenshot)
            print(f"🔤 验证码识别结果: {captcha_text}")

            return captcha_text

        except Exception as e:
            print(f"❌ 验证码识别失败: {e}")
            return None

    def input_captcha(self, captcha_text):
        """输入验证码"""
        try:
            print(f"🔤 正在输入验证码: {captcha_text}")

            # 查找验证码输入框 - 基于实际HTML结构
            captcha_selectors = [
                "input[placeholder='请输入验证码']",  # 实际的placeholder
                "input[placeholder*='验证码']",
                "input[placeholder*='captcha']",
                "input[name='captcha']",
                "input[name='captcha_code']",
                ".captcha input",
                ".verify input"
            ]

            captcha_input = None
            for selector in captcha_selectors:
                try:
                    captcha_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue

            if captcha_input:
                captcha_input.clear()
                captcha_input.send_keys(captcha_text)
                print("✅ 验证码输入成功！")
                return True
            else:
                print("❌ 找不到验证码输入框")
                return False

        except Exception as e:
            print(f"❌ 输入验证码失败: {e}")
            return False

    def input_gift_code(self, gift_code):
        """输入兑换码"""
        try:
            print(f"🎁 正在输入兑换码: {gift_code}")

            # 查找兑换码输入框 - 基于实际HTML结构
            gift_code_selectors = [
                "input[placeholder='请输入兑换码']",  # 实际的placeholder
                "input[placeholder*='兑换码']",
                "input[placeholder*='Gift Code']",
                "input[placeholder*='礼品码']",
                "input[name='cdk']",
                "input[name='gift_code']",
                ".cdk input",
                "#cdk"
            ]

            gift_input = None
            for selector in gift_code_selectors:
                try:
                    gift_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue

            if gift_input:
                gift_input.clear()
                gift_input.send_keys(gift_code)
                print("✅ 兑换码输入成功！")
                return True
            else:
                print("❌ 找不到兑换码输入框")
                return False

        except Exception as e:
            print(f"❌ 输入兑换码失败: {e}")
            return False

    def click_exchange_button(self):
        """点击确定兑换按钮"""
        try:
            print("🔄 正在点击确定兑换按钮...")

            # 查找兑换按钮 - 基于实际HTML结构
            button_selectors = [
                ".btn.exchange_btn",  # 实际的确认兑换按钮class
                "div.exchange_btn",
                ".exchange_btn",
                "div.btn.exchange_btn",
                "button:contains('确定兑换')",
                "div:contains('确定兑换')",
                "button:contains('兑换')",
                "div:contains('兑换')",
                "button:contains('Exchange')",
                "button:contains('确认')",
                "button:contains('确定')",
                ".submit-btn",
                ".confirm-btn",
                "input[type='submit']",
                "button[type='submit']"
            ]

            exchange_button = None
            for selector in button_selectors:
                try:
                    if ":contains(" in selector:
                        # 使用XPath查找包含文本的按钮
                        text = selector.split(":contains('")[1].split("')")[0]
                        exchange_button = self.driver.find_element(By.XPATH, f"//button[contains(text(), '{text}')]")
                    else:
                        exchange_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue

            if not exchange_button:
                # 尝试查找所有按钮，根据文本内容判断
                buttons = self.driver.find_elements(By.TAG_NAME, "button")
                for btn in buttons:
                    btn_text = btn.text.lower()
                    if any(keyword in btn_text for keyword in ["确定兑换", "兑换", "exchange", "确认", "确定", "submit"]):
                        exchange_button = btn
                        break

            if exchange_button:
                # 滚动到按钮位置
                self.driver.execute_script("arguments[0].scrollIntoView();", exchange_button)
                time.sleep(1)

                # 点击按钮
                exchange_button.click()
                print("✅ 确定兑换按钮点击成功！")
                return True
            else:
                print("❌ 找不到确定兑换按钮")
                return False

        except Exception as e:
            print(f"❌ 点击确定兑换按钮失败: {e}")
            return False

    def wait_for_result(self):
        """等待兑换结果"""
        try:
            print("⏳ 正在等待兑换结果...")

            # 等待结果出现
            time.sleep(3)

            # 首先检查是否有弹出对话框（基于3.html结构）
            dialog_result = self.check_dialog_result()
            if dialog_result:
                return dialog_result

            # 查找结果消息
            result_selectors = [
                ".result-message",
                ".success-message",
                ".error-message",
                ".alert",
                ".message",
                ".toast",
                ".modal_content .msg",  # 基于3.html的对话框结构
                ".message_modal .msg"   # 基于3.html的对话框结构
            ]

            result_element = None
            for selector in result_selectors:
                try:
                    result_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if result_element.is_displayed():
                        break
                except NoSuchElementException:
                    continue

            if result_element:
                result_text = result_element.text
                print(f"📋 兑换结果: {result_text}")
                return result_text
            else:
                # 如果找不到特定的结果元素，检查页面是否有变化
                print("📋 兑换请求已发送，请检查游戏内邮箱")
                return "兑换请求已发送"

        except Exception as e:
            print(f"❌ 获取兑换结果失败: {e}")
            return "获取结果失败"

    def check_dialog_result(self):
        """检查弹出对话框中的结果（基于3.html结构）"""
        try:
            # 基于3.html的对话框选择器
            dialog_selectors = [
                ".message_modal",  # 3.html中的对话框类名
                ".modal_content",  # 对话框内容
                ".modal",
                ".dialog",
                ".popup",
                "[role='dialog']"
            ]

            for selector in dialog_selectors:
                try:
                    dialog = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if dialog.is_displayed():
                        # 获取对话框文本内容
                        dialog_text = dialog.text
                        print(f"🔍 检测到对话框: {dialog_text}")

                        # 尝试点击确定按钮关闭对话框
                        self.close_dialog(dialog)

                        return dialog_text
                except NoSuchElementException:
                    continue

            return None

        except Exception as e:
            print(f"⚠️ 检查对话框时出错: {e}")
            return None

    def close_dialog(self, dialog):
        """关闭对话框（基于3.html结构）"""
        try:
            # 基于3.html的按钮选择器
            button_selectors = [
                ".confirm_btn",     # 3.html中的确定按钮
                ".close_btn",       # 3.html中的关闭按钮
                "button:contains('确定')",
                "button:contains('确认')",
                "button:contains('OK')",
                "button:contains('关闭')",
                ".btn-confirm",
                ".btn-ok"
            ]

            for selector in button_selectors:
                try:
                    if ":contains(" in selector:
                        # 使用XPath查找包含文本的按钮
                        text = selector.split(":contains('")[1].split("')")[0]
                        button = dialog.find_element(By.XPATH, f".//button[contains(text(), '{text}')]")
                    else:
                        button = dialog.find_element(By.CSS_SELECTOR, selector)

                    if button.is_displayed():
                        button.click()
                        print("✅ 已关闭对话框")
                        time.sleep(1)
                        return True
                except NoSuchElementException:
                    continue

            # 如果找不到按钮，尝试按ESC键
            from selenium.webdriver.common.keys import Keys
            dialog.send_keys(Keys.ESCAPE)
            print("✅ 已通过ESC键关闭对话框")
            time.sleep(1)
            return True

        except Exception as e:
            print(f"⚠️ 关闭对话框失败: {e}")
            return False

    def analyze_result(self, result_text):
        """
        分析兑换结果文本，返回结果状态

        Returns:
            str: "success" - 新兑换成功
                 "already_claimed" - 已经领取过（也算成功）
                 "failed" - 兑换失败
        """
        if not result_text:
            return "failed"

        result_lower = result_text.lower()

        # 新兑换成功关键词
        success_keywords = [
            "成功", "success", "兑换成功", "领取成功", "已发放",
            "发放成功", "获得", "领取完成", "兑换完成", "奖励已发送"
        ]

        # 已领取关键词（根据3.html分析）
        already_claimed_keywords = [
            "已领取", "已经领取", "重复领取", "无法再次领取",
            "该礼物已领取", "已经兑换", "重复兑换", "已使用",
            "您已领取过该礼物", "不能重复领取", "已经获得过","您已领取过该礼物，无法再次领取。",
            "duplicate", "already claimed", "already received"
        ]

        # 检查是否已领取（这也算是一种成功状态）
        if any(keyword in result_lower for keyword in already_claimed_keywords):
            return "already_claimed"

        # 检查是否新兑换成功
        if any(keyword in result_lower for keyword in success_keywords):
            return "success"

        # 其他情况视为失败
        return "failed"

    def exchange_gift_code(self, player_id, gift_code, max_retries=3):
        """
        执行完整的兑换流程

        Args:
            player_id (str): 玩家ID
            gift_code (str): 兑换码
            max_retries (int): 最大重试次数

        Returns:
            bool: 兑换是否成功
        """
        for attempt in range(max_retries):
            try:
                print(f"\n🎯 开始第 {attempt + 1} 次兑换尝试...")

                # 1. 打开网站
                self.open_website()

                # 2. 输入玩家ID
                if not self.input_player_id(player_id):
                    continue

                # 3. 点击登录按钮获取角色信息
                if not self.click_login_button():
                    continue

                print("📋 等待角色信息加载...")
                time.sleep(2)

                # 4. 输入兑换码
                if not self.input_gift_code(gift_code):
                    continue

                # 5. 检查是否有验证码（根据HTML结构，可能没有验证码）
                print("🔍 检查是否需要验证码...")
                captcha_text = self.get_captcha_and_solve()
                if captcha_text:
                    if self.input_captcha(captcha_text):
                        print("✅ 验证码输入成功")
                    else:
                        print("⚠️ 验证码处理失败，继续尝试...")
                        if attempt < max_retries - 1:
                            continue
                else:
                    print("ℹ️ 未发现验证码，直接进行兑换")

                # 6. 点击确定兑换按钮
                if not self.click_exchange_button():
                    continue

                # 7. 等待兑换结果
                result = self.wait_for_result()

                # 8. 分析结果状态
                result_status = self.analyze_result(result)

                if result_status == "success":
                    print("🎉 兑换成功！")
                    return True
                elif result_status == "already_claimed":
                    print("✅ 该兑换码已经领取过了（算作成功）")
                    return True  # 已领取也算成功
                else:
                    print(f"❌ 兑换失败: {result}")
                    if attempt < max_retries - 1:
                        print("🔄 准备重试...")
                        time.sleep(2)

            except Exception as e:
                print(f"❌ 兑换过程出错: {e}")
                if attempt < max_retries - 1:
                    print("🔄 准备重试...")
                    time.sleep(2)

        print("❌ 所有重试都失败了")
        return False

    def exchange_gift_code_with_details(self, player_id, gift_code, max_retries=3):
        """
        执行兑换并返回详细结果

        Returns:
            tuple: (success: bool, result_info: dict)
        """
        for attempt in range(max_retries):
            try:
                print(f"\n🔄 开始第 {attempt + 1} 次兑换尝试...")

                # 执行兑换流程
                self.open_website()
                if not self.input_player_id(player_id):
                    continue
                if not self.click_login_button():
                    continue
                time.sleep(2)
                if not self.input_gift_code(gift_code):
                    continue

                captcha_text = self.get_captcha_and_solve()
                if captcha_text:
                    self.input_captcha(captcha_text)

                if not self.click_exchange_button():
                    continue

                result = self.wait_for_result()
                result_status = self.analyze_result(result)

                if result_status == "success":
                    print("🎉 兑换成功！")
                    return True, {"status": "new_success", "message": result}
                elif result_status == "already_claimed":
                    print("✅ 该兑换码已经领取过了（算作成功）")
                    return True, {"status": "already_claimed", "message": result}
                else:
                    if attempt < max_retries - 1:
                        print("🔄 准备重试...")
                        time.sleep(2)

            except Exception as e:
                print(f"❌ 兑换过程出错: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)

        return False, {"status": "failed", "message": "所有重试都失败了"}

    def close(self):
        """关闭浏览器"""
        if self.driver:
            print("🔚 正在关闭浏览器...")
            self.driver.quit()
            print("✅ 浏览器已关闭")

def main():
    """主函数"""
    print("🎮 WJDR 兑换码自动化工具")
    print("=" * 50)

    # 获取用户输入
    player_id = input("请输入玩家ID: ").strip()
    gift_code = input("请输入兑换码: ").strip()

    if not player_id or not gift_code:
        print("❌ 玩家ID和兑换码不能为空")
        return

    # 询问是否使用无头模式
    headless_choice = input("是否使用无头模式？(y/n，默认n): ").strip().lower()
    headless = headless_choice == 'y'

    # 创建自动化实例
    automation = None
    try:
        automation = WJDRAutoExchange(headless=headless)

        # 执行兑换
        success = automation.exchange_gift_code(player_id, gift_code)

        if success:
            print("\n🎉 兑换完成！请检查游戏内邮箱获取奖励。")
        else:
            print("\n❌ 兑换失败，请检查输入信息或手动尝试。")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
    finally:
        if automation:
            automation.close()

if __name__ == "__main__":
    main()
