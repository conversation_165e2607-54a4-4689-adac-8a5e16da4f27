# WJDR 兑换码自动化工具

> 🎮 **无尽的拉格朗日** 兑换码自动兑换工具  
> 🤖 **浏览器自动化** - 绕过API签名验证，模拟真实用户操作  
> 🔤 **智能验证码识别** - 自动OCR识别，无需手动输入  

## ✨ 功能特点

- ✅ **完全自动化** - 从输入角色ID到兑换成功的全流程自动化
- 🔓 **绕过签名验证** - 使用真实浏览器操作，无需破解复杂的API签名
- 🤖 **智能验证码识别** - 集成OCR引擎，自动识别并输入验证码
- 📦 **批量处理** - 支持多个角色和多个兑换码的批量兑换
- 🔄 **智能重试** - 失败时自动重试，已领取状态智能跳过
- 📊 **详细报告** - 生成完整的兑换结果统计报告
- 🎮 **动态配置管理** - 交互式界面管理角色和兑换码，无需手动编辑文件
- 💾 **自动备份** - 配置修改时自动备份，数据安全有保障
- 🔍 **智能状态识别** - 自动识别"已领取"状态，避免无效重试

## 🚀 快速开始

### 1. 环境准备

确保您的系统已安装：
- **Python 3.7+**
- **Chrome浏览器** (会自动下载ChromeDriver)

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置兑换信息

编辑 `config.json` 文件：

```json
{
  "players": {
    "主号": "123333938",
    "小号": "987654321"
  },
  "gift_codes": [
    "PENGYOU666"
  ],
  "delay": 5,
  "settings": {
    "max_retries": 3,
    "timeout": 30,
    "auto_ocr": true,
    "save_captcha": false
  }
}
```

### 4. 开始使用

#### 方法一：动态配置管理（推荐）
```bash
# Windows用户
run_config_manager.bat

# 或直接运行
python config_manager.py
```
通过交互式界面管理角色和兑换码，然后直接启动批量兑换。

#### 方法二：批量兑换
```bash
# Windows用户
run_batch.bat

# 或直接运行
python batch_exchange.py
```

#### 方法三：单次兑换
```bash
# Windows用户
run_browser.bat

# 或直接运行
python browser_automation.py
```

## 📋 配置说明

### config.json 参数详解

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `players` | 对象/数组 | 角色ID配置 | `{"主号": "123333938", "小号": "987654321"}` 或 `["123333938", "987654321"]` |
| `gift_codes` | 数组 | 兑换码列表 | `["PENGYOU666", "WJDR2024"]` |
| `delay` | 数字 | 兑换间隔时间(秒) | `5` |
| `settings.max_retries` | 数字 | 最大重试次数 | `3` |
| `settings.timeout` | 数字 | 超时时间(秒) | `30` |
| `settings.auto_ocr` | 布尔 | 自动OCR识别 | `true` |
| `settings.save_captcha` | 布尔 | 保存验证码图片 | `false` |

### 🆕 新的角色配置格式

**推荐使用对象格式**（更容易记忆和管理）：
```json
{
  "players": {
    "主号": "123333938",
    "小号1": "987654321",
    "小号2": "111222333",
    "朋友账号": "444555666"
  }
}
```

**仍支持旧的数组格式**（向后兼容）：
```json
{
  "players": [
    "123333938",
    "987654321",
    "111222333"
  ]
}
```

## 🎮 动态配置管理器

### 功能特点
- **交互式界面**: 友好的命令行菜单系统
- **实时管理**: 无需手动编辑JSON文件
- **自动备份**: 每次修改自动创建备份
- **数据验证**: 防止重复和格式错误
- **搜索功能**: 快速查找角色和兑换码
- **批量操作**: 支持批量导入兑换码

### 启动配置管理器
```bash
# Windows用户
run_config_manager.bat

# 或直接运行
python config_manager.py
```

### 主要功能

#### 👤 角色管理
- ➕ **添加角色**: 为角色ID设置易记名称
- ❌ **删除角色**: 移除不需要的角色
- ✏️ **修改角色**: 更新角色信息
- 🔍 **搜索角色**: 按名称或ID查找

#### 🎁 兑换码管理
- ➕ **添加兑换码**: 单个添加新兑换码
- ❌ **删除兑换码**: 移除过期兑换码
- ✏️ **修改兑换码**: 更正兑换码内容
- 🔍 **搜索兑换码**: 按关键词查找
- 📥 **批量导入**: 一次性导入多个兑换码

#### ⚙️ 设置管理
- 🕐 **兑换间隔**: 调整兑换等待时间
- 🔄 **重试次数**: 设置失败重试次数
- ⏱️ **超时时间**: 配置操作超时
- 🤖 **OCR设置**: 自动验证码识别开关
- 💾 **验证码保存**: 是否保存验证码图片

#### 🚀 一键启动
配置完成后可直接在管理器中启动批量兑换，无需切换程序。

## 🔧 工作原理

### 兑换流程
1. **打开兑换网站** - 自动访问官方兑换页面
2. **输入角色ID** - 自动填入配置的角色ID
3. **点击登录** - 获取角色信息和验证码
4. **输入兑换码** - 自动填入要兑换的礼品码
5. **识别验证码** - OCR自动识别验证码图片
6. **输入验证码** - 自动填入识别结果
7. **确认兑换** - 点击兑换按钮并获取结果

### 技术特点
- **浏览器自动化**: 使用Selenium模拟真实用户操作
- **OCR识别**: 集成ddddocr引擎自动识别验证码
- **智能重试**: 验证码识别失败时自动重试
- **元素定位**: 基于实际页面结构精确定位元素
- **智能状态识别**: 自动识别兑换结果状态，区分成功、失败、已领取
- **对话框处理**: 自动检测和处理弹出对话框

## 📊 结果报告

兑换完成后会生成详细报告：

### 控制台输出
```
📊 批量兑换总结
==================================================
总兑换次数: 3
✅ 成功次数: 1
ℹ️ 已领取次数: 1
❌ 失败次数: 1
成功率: 33.3%
有效率: 66.7%

📋 详细结果:
  玩家 主号(123333938):
    ✅ 成功: 1/3 (33.3%)
    ℹ️ 已领取: 1/3 (33.3%)
    ❌ 失败: 1/3 (33.3%)
```

### 兑换状态说明
- **✅ 成功**: 兑换码成功兑换，获得奖励
- **ℹ️ 已领取**: 该兑换码之前已经使用过，无法重复领取
- **❌ 失败**: 兑换过程中出现错误或兑换码无效
- **有效率**: (成功 + 已领取) / 总次数，表示兑换码的有效性

### JSON报告文件
自动保存为 `exchange_results_YYYYMMDD_HHMMSS.json`，包含：
- 每个角色的兑换统计
- 每个兑换码的详细结果
- 失败原因和错误信息

## 🛠️ 故障排除

### 常见问题

**Q: 浏览器启动失败**
- A: 确保已安装Chrome浏览器，程序会自动下载ChromeDriver

**Q: 验证码识别失败**
- A: OCR识别准确率约85-90%，失败时会自动重试

**Q: 找不到页面元素**
- A: 网站可能更新了页面结构，请检查是否有程序更新

**Q: 兑换失败显示"已领取"**
- A: 该兑换码已经使用过，程序会自动识别并跳过重试，直接处理下一个兑换码

**Q: 配置文件格式错误**
- A: 请确保config.json文件使用UTF-8编码保存，不要包含BOM标记
- A: 可以参考config_example.json文件格式

**Q: 程序如何处理重复兑换的情况？**
- A: 程序会自动识别"已领取"状态，包括以下关键词：
  - "已领取"、"已经领取"、"重复领取"、"无法再次领取"
  - "您已领取过该礼物"、"不能重复领取"、"已经兑换"
- A: 检测到已领取状态时，程序会跳过重试，直接处理下一个兑换码
- A: 在统计报告中，已领取的兑换码会单独计算，不影响成功率

### 调试模式

如需观察兑换过程，可以修改 `batch_exchange.py` 中的设置：
```python
automation = WJDRAutoExchange(headless=False)  # 显示浏览器窗口
```

## 📁 文件结构

```
wjdr/
├── browser_automation.py       # 浏览器自动化核心模块
├── batch_exchange.py          # 批量兑换处理模块
├── config_manager.py          # 动态配置管理器
├── config.json               # 配置文件
├── config_example.json       # 配置文件示例
├── requirements.txt          # 依赖包列表
├── run_browser.bat           # 单次兑换启动脚本
├── run_batch.bat            # 批量兑换启动脚本
├── run_config_manager.bat    # 配置管理器启动脚本
├── config_backups/           # 配置备份目录
├── results/                  # 兑换结果目录
├── 快速使用指南.md           # 快速使用指南
├── 配置管理器使用说明.md      # 配置管理器说明
└── README.md                # 详细使用说明
```

## ⚠️ 注意事项

1. **合理使用**: 请勿频繁请求，避免对服务器造成压力
2. **兑换限制**: 每个兑换码通常只能使用一次
3. **网络环境**: 确保网络连接稳定
4. **浏览器版本**: 保持Chrome浏览器为最新版本

## 🔄 更新日志

### v2.0.0 (当前版本)
- ✅ 实现完整的浏览器自动化方案
- ✅ 成功绕过API签名验证问题
- ✅ 集成OCR自动验证码识别
- ✅ 支持批量处理多个角色和兑换码
- ✅ 添加智能重试和错误处理机制
- ✅ 生成详细的兑换结果报告

### v1.0.0 (已废弃)
- ❌ API方案因签名验证问题无法使用

## 📞 支持

如果遇到问题或需要帮助，请：
1. 检查本文档的故障排除部分
2. 确认配置文件格式正确
3. 尝试重新安装依赖包

## 📄 许可证

本项目仅供学习和个人使用，请勿用于商业用途。

---

🎉 **祝您兑换愉快！** 🎉
