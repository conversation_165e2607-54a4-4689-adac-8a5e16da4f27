# 🚀 WJDR 兑换码自动化工具 - 快速使用指南

## 🎯 两种使用方式

### 📦 方式一：批量兑换（推荐）
**适合多个角色和多个兑换码**
```bash
双击 run_batch.bat
```
- ✅ 支持多角色批量处理
- ✅ 智能重试机制
- ✅ 详细统计报告
- ✅ 自动保存结果

### 🔍 方式二：单次兑换
**适合测试或单个兑换码**
```bash
双击 run_browser.bat
```
- ✅ 交互式输入
- ✅ 实时观察过程
- ✅ 适合调试

## 📋 快速配置

### 1️⃣ 安装依赖
```bash
pip install -r requirements.txt
```

### 2️⃣ 配置角色和兑换码
编辑 `config.json` 文件：
```json
{
  "players": {
    "主号": "您的主角色ID",
    "小号": "您的小号角色ID"
  },
  "gift_codes": [
    "您的兑换码"
  ],
  "delay": 5,
  "settings": {
    "max_retries": 3,
    "timeout": 30,
    "auto_ocr": true,
    "save_captcha": false
  }
}
```

### 3️⃣ 开始兑换
```bash
# 批量兑换（推荐）
双击 run_batch.bat

# 单次兑换
双击 run_browser.bat
```

## ✅ 兑换结果说明

### 🎉 三种兑换状态
- **🎉 新成功**: 第一次成功兑换该兑换码
- **✅ 已领取**: 之前已经兑换过（也算成功，不会重试）
- **❌ 失败**: 兑换码无效或网络错误

### 📊 统计报告示例
```
📊 批量兑换总结
==================================================
总兑换次数: 10
🎉 新成功次数: 3
✅ 已领取次数: 5
📈 总成功次数: 8
❌ 失败次数: 2
成功率: 80.0%
```

### 🔄 智能重试机制
- 新成功 → 继续下一个
- 已领取 → 跳过重试，继续下一个
- 失败 → 自动重试最多3次

## 🎯 核心文件说明

| 文件 | 说明 |
|------|------|
| `browser_automation.py` | 浏览器自动化核心模块 |
| `batch_exchange.py` | 批量兑换处理模块 |
| `config.json` | 配置文件（角色ID和兑换码） |
| `config_example.json` | 配置文件示例 |
| `requirements.txt` | 依赖包列表 |
| `run_batch.bat` | 批量兑换启动脚本 |
| `3.html` | 已兑换页面示例（用于测试） |

## ⚙️ 配置参数说明

### 基本配置
- **players**: 角色配置（对象格式，支持易记名称）
- **gift_codes**: 兑换码列表
- **delay**: 兑换间隔时间（秒）

### 高级设置
- **max_retries**: 最大重试次数（默认3次）
- **timeout**: 超时时间（默认30秒）
- **auto_ocr**: 自动OCR识别（默认开启）
- **save_captcha**: 保存验证码图片（默认关闭）

## 🔧 常见问题

**Q: 如何添加多个角色？**
```json
{
  "players": {
    "主号": "角色ID1",
    "小号1": "角色ID2",
    "小号2": "角色ID3",
    "朋友账号": "角色ID4"
  }
}
```

**Q: 还支持旧的数组格式吗？**
```json
{
  "players": [
    "角色ID1",
    "角色ID2",
    "角色ID3"
  ]
}
```
是的，仍然支持旧格式，但推荐使用新的对象格式，更容易管理。

**Q: 如何添加多个兑换码？**
```json
{
  "gift_codes": [
    "兑换码1",
    "兑换码2",
    "兑换码3"
  ]
}
```

**Q: 验证码识别失败怎么办？**
- 程序会自动重试3次
- 识别准确率约85-90%
- 失败时会显示相关提示

**Q: 显示"已领取"是什么意思？**
- 该兑换码已经使用过，现在算作成功状态
- 程序不会重试，直接处理下一个兑换码
- 在统计中计入"已领取次数"

**Q: 如何查看兑换结果？**
- 控制台会实时显示兑换进度和结果
- 自动生成JSON格式的详细报告
- 结果文件按日期分层保存在 `results/` 目录

**Q: 程序卡住了怎么办？**
- 按 Ctrl+C 可以中断程序
- 检查网络连接是否正常
- 确认Chrome浏览器是否正常启动

## 🎉 使用流程

### 新用户推荐流程
1. **安装依赖** → `pip install -r requirements.txt`
2. **配置信息** → 编辑 `config.json` 文件
3. **启动批量兑换** → 双击 `run_batch.bat`
4. **查看结果** → 自动生成详细统计报告

### 快速测试流程
1. **单次兑换测试** → 双击 `run_browser.bat`
2. **输入角色ID和兑换码** → 按提示操作
3. **观察兑换过程** → 验证功能是否正常

## 💡 小贴士

- 🔄 已兑换过的兑换码现在算作成功，不会浪费时间重试
- 📁 结果文件按日期分层保存：`results/YYYY/MM/DD/`
- 🎯 推荐先用单次兑换测试，确认无误后再批量处理
- 📊 批量兑换会生成详细的统计报告和JSON结果文件
- ⚙️ 可以通过修改 `config.json` 中的 `settings` 调整高级参数

详细功能说明请查看 `README.md` 文件。
